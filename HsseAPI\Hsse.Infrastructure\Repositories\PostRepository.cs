﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class PostRepository : IPostRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        public PostRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }
        public PostsCountsDto GetPostsCountsByUserId(int userId)
        {
            var userPosts = _MasterDBContext.MstPosts.Where(p => !p.IsDeleted && (p.UserId == userId || p.ClosedBy == userId)).ToList();

            var countsDto = new PostsCountsDto
            {
                NewPosts = userPosts.Count(p => p.UserId == userId),
                Closure = userPosts.Count(p => p.ClosedBy == userId),
                Points = 500
            };

            return countsDto;

        }
        public long CreatePost(CreatePostDto createPostDto)
        {
            if (createPostDto.PostID == 0)
            {
                // Create new post
                var newPost = new MstPost
                {
                    UserId = createPostDto.UserID,
                    FacilityId = createPostDto.FacilityID,
                    Title = createPostDto.Title,
                    Description = createPostDto.Description,
                    PostType = createPostDto.PostType,
                    Location = createPostDto.Location,
                    TaggedCategoryId = createPostDto.TaggedCategoryId,
                    RequiresFollowup = createPostDto.RequiresFollowup,
                    Status = createPostDto.Status,
                    CreatedAt = DateTime.Now,
                    IsDeleted = false
                };

                _MasterDBContext.MstPosts.Add(newPost);
                _MasterDBContext.SaveChanges();

                if (!string.IsNullOrEmpty(createPostDto.ImageBase64))
                {
                    var media = new MstPostMedium
                    {
                        PostId = newPost.PostId,
                        MediaUrl = createPostDto.ImageBase64,
                        CreatedAt = DateTime.Now
                    };

                    _MasterDBContext.MstPostMedia.Add(media);
                    _MasterDBContext.SaveChanges();
                }

                return 1;
            }
            else
            {
                // Update existing post
                var existingPost = _MasterDBContext.MstPosts
                    .FirstOrDefault(p => p.PostId == createPostDto.PostID && !p.IsDeleted);

                if (existingPost == null)
                    return 0;

                existingPost.UserId = createPostDto.UserID;
                existingPost.FacilityId = createPostDto.FacilityID;
                existingPost.Title = createPostDto.Title;
                existingPost.Description = createPostDto.Description;
                existingPost.PostType = createPostDto.PostType;
                existingPost.Location = createPostDto.Location;
                existingPost.TaggedCategoryId = createPostDto.TaggedCategoryId;
                existingPost.RequiresFollowup = createPostDto.RequiresFollowup;
                existingPost.Status = createPostDto.Status;
                existingPost.UpdatedAt = DateTime.Now;

                var existingMedia = _MasterDBContext.MstPostMedia
                    .FirstOrDefault(m => m.PostId == createPostDto.PostID);

                if (!string.IsNullOrEmpty(createPostDto.ImageBase64))
                {
                    if (existingMedia != null)
                    {
                        existingMedia.MediaUrl = createPostDto.ImageBase64;
                    }
                    else
                    {
                        var newMedia = new MstPostMedium
                        {
                            PostId = createPostDto.PostID,
                            MediaUrl = createPostDto.ImageBase64,
                            CreatedAt = DateTime.Now
                        };
                        _MasterDBContext.MstPostMedia.Add(newMedia);
                    }
                }

                _MasterDBContext.SaveChanges();
                return 2;
            }
        }
        public List<PostDetailsDto> GetPosts(int? userId = null)
        {
            var postsQuery = _MasterDBContext.MstPosts
                .Where(p => !p.IsDeleted);

            if (userId.HasValue)
                postsQuery = postsQuery.Where(p => p.UserId == userId.Value);

            var posts = postsQuery
                .Select(post => new PostDetailsDto
                {
                    PostID = post.PostId,
                    UserID = post.UserId,
                    Title = post.Title,
                    Description = post.Description,
                    PostType = post.PostType,
                    Location = post.Location,
                    TaggedCategoryId = post.TaggedCategoryId,
                    RequiresFollowup = post.RequiresFollowup,
                    Status = post.Status,
                    CreatedAt = post.CreatedAt,
                    CommentsCount = _MasterDBContext.MstPostComments.Count(c => c.PostId == post.PostId),
                    Comments = _MasterDBContext.MstPostComments
                        .Where(c => c.PostId == post.PostId)
                        .Select(c => new CommentDto
                        {
                            CommentID = c.CommentId,
                            ParentCommentID = c.ParentCommentId,
                            UserID = c.UserId,
                            CommentText = c.CommentText,
                            CommentedAt = c.CommentedAt,
                            LikesCount = _MasterDBContext.MstCommentLikes.Count(l => l.CommentId == c.CommentId && l.IsLiked),
                            Likes = _MasterDBContext.MstCommentLikes
                                .Where(l => l.CommentId == c.CommentId)
                                .Select(l => new CommentLikeDto
                                {
                                    CommentLikeId = l.CommentLikeId,
                                    CommentId = l.CommentId,
                                    UserId = l.UserId
                                }).ToList()
                        }).ToList(),
                    LikesCount = _MasterDBContext.MstLikesConfigs.Count(l => l.PostId == post.PostId && l.IsLiked),
                    Likes = _MasterDBContext.MstLikesConfigs
                        .Where(l => l.PostId == post.PostId)
                        .Select(l => new LikeDto
                        {
                            LikeID = l.LikeId,
                            UserID = l.UserId,
                            IsLiked = l.IsLiked,
                            LikedAt = l.LikedAt
                        }).ToList(),
                    MediaUrls = _MasterDBContext.MstPostMedia
                        .Where(m => m.PostId == post.PostId)
                        .Select(m => m.MediaUrl)
                        .ToList()
                })
                .OrderByDescending(p => p.CreatedAt)
                .ToList();

            return posts;
        }

        public List<MstPostCategory> GetPostCategories()
        {
            return _MasterDBContext.MstPostCategories.ToList();
        }
        public int CreateOrUpdateLikes(CreateLikeDto createLikeDto)
        {
            // Ensure exactly one of PostID or EventID is provided
            if ((createLikeDto.PostID == null && createLikeDto.EventID == null) ||
                (createLikeDto.PostID != null && createLikeDto.EventID != null))
            {
                throw new ArgumentException("Like must be for either a Post or an Event, not both.");
            }

            // Query only for one of the non-null keys
            var result = _MasterDBContext.MstLikesConfigs.FirstOrDefault(x =>
                x.UserId == createLikeDto.UserID &&
                ((createLikeDto.PostID != null && x.PostId == createLikeDto.PostID) ||
                 (createLikeDto.EventID != null && x.EventId == createLikeDto.EventID)));

            if (result == null)
            {
                var newLike = new MstLikesConfig
                {
                    UserId = createLikeDto.UserID,
                    PostId = createLikeDto.PostID,
                    EventId = createLikeDto.EventID,
                    IsLiked = createLikeDto.IsLiked,
                    LikedAt = DateTime.Now
                };

                _MasterDBContext.MstLikesConfigs.Add(newLike);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }

            // Update existing
            result.IsLiked = createLikeDto.IsLiked;
            result.LikedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 2; // Updated
        }

        public int ClosePost(ClosedPostDto closedPostDto)
        {
            var post = _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostId == closedPostDto.PostID && !p.IsDeleted);

            if (post == null)
                return 0; // Post not found

            post.ClosedBy = closedPostDto.UserID;
            post.ClosedDescription = closedPostDto.ClosedDescription;
            post.UpdatedAt = DateTime.Now;
            // Assuming status 2 means closed - adjust based on your business logic
            post.Status = 2;
            _MasterDBContext.SaveChanges();

            var existingMedia = _MasterDBContext.MstPostMedia
                   .FirstOrDefault(m => m.PostId == closedPostDto.PostID);

            if (!string.IsNullOrEmpty(closedPostDto.ImageBase64))
            {
                if (existingMedia != null)
                {
                    existingMedia.AfterMediaUrl = closedPostDto.ImageBase64;
                }
                else
                {
                    var newMedia = new MstPostMedium
                    {
                        PostId = closedPostDto.PostID,
                        AfterMediaUrl = closedPostDto.ImageBase64,
                        CreatedAt = DateTime.Now
                    };
                    _MasterDBContext.MstPostMedia.Add(newMedia);
                }
            }

            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int DeletePost(int postId, int deletedBy)
        {
            var post = _MasterDBContext.MstPosts
                .FirstOrDefault(p => p.PostId == postId && !p.IsDeleted);

            if (post == null)
                return 0; // Post not found

            post.DeletedBy = deletedBy;
            post.IsDeleted = true;
            post.UpdatedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 1; // Success
        }

        public int CreateOrUpdateComment(CreateCommentDto createCommentDto)
        {
            if (createCommentDto.CommentID == 0)
            {
                // Create new comment
                var newComment = new MstPostComment
                {
                    PostId = createCommentDto.PostID,
                    UserId = createCommentDto.UserID,
                    CommentText = createCommentDto.CommentText,
                    ParentCommentId = createCommentDto.ParentCommentID,
                    CommentedAt = DateTime.Now
                };

                _MasterDBContext.MstPostComments.Add(newComment);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing comment
                var existingComment = _MasterDBContext.MstPostComments
                    .FirstOrDefault(c => c.CommentId == createCommentDto.CommentID);

                if (existingComment == null)
                    return 0; // Comment not found

                existingComment.CommentText = createCommentDto.CommentText;
                existingComment.CommentedAt = DateTime.Now;

                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }
        public int CreateOrUpdateCommentLikes(CommentLikeCreateDto commentLikeDto)
        {
            // Ensure CommentID is provided
            if (commentLikeDto.CommentId == 0)
            {
                throw new ArgumentException("CommentID must be provided for liking a comment.");
            }
            // Check if the like already exists
            var existingLike = _MasterDBContext.MstCommentLikes
                .FirstOrDefault(x => x.UserId == commentLikeDto.UserId && x.CommentId == commentLikeDto.CommentId);
            if (existingLike == null)
            {
                // Create new like
                var newLike = new MstCommentLike
                {
                    UserId = commentLikeDto.UserId,
                    CommentId = commentLikeDto.CommentId,
                    IsLiked = commentLikeDto.IsLiked,
                    CreatedAt = DateTime.Now
                };
                _MasterDBContext.MstCommentLikes.Add(newLike);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }
            else
            {
                // Update existing like
                existingLike.IsLiked = commentLikeDto.IsLiked;
                existingLike.CreatedAt = DateTime.Now;
                _MasterDBContext.SaveChanges();
                return 2; // Updated
            }
        }
    }
}
