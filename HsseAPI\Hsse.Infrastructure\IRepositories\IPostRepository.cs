﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Dto.Response;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IPostRepository
    {
        PostsCountsDto GetPostsCountsByUserId(int userId);
        long CreatePost(CreatePostDto createPostDto);
        List<PostDetailsDto> GetPosts(int? userId = null);
        List<MstPostCategory> GetPostCategories();
        int CreateOrUpdateLikes(CreateLikeDto createLikeDto);
        int ClosePost(ClosedPostDto closedPostDto);
        int DeletePost(int postId, int deletedBy);
        int CreateOrUpdateComment(CreateCommentDto createCommentDto);
        int CreateOrUpdateCommentLikes(CommentLikeCreateDto commentLikeDto);
    }
}
